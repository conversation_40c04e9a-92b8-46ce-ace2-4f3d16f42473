# Electron + Go 基础框架设计文档

## 项目概述

### 技术架构

- **前端**: Electron + Vue 3 + Vite + Element Plus
- **后端**: Go 1.24.5 + Gin
- **通信**: HTTP API
- **打包**: Electron Builder

### 架构图

```
┌─────────────────┐    HTTP API    ┌─────────────────┐
│   Electron App  │ ◄─────────────► │   Go Backend    │
│                 │                 │                 │
│ ┌─────────────┐ │                 │ ┌─────────────┐ │
│ │ Main Process│ │                 │ │   API Server│ │
│ └─────────────┘ │                 │ └─────────────┘ │
│ ┌─────────────┐ │                 └─────────────────┘
│ │Renderer Proc│ │
│ │(Vue 3 + UI) │ │
│ └─────────────┘ │
└─────────────────┘
```

## 目录结构

```
spreader-tron-app/               # 项目根目录
├── electron-app/                # Electron前端应用 (Vite创建)
│   ├── public/                  # 静态资源
│   │   └── vite.svg
│   ├── src/                     # Vue 3 + TypeScript 源码
│   │   ├── components/          # Vue组件
│   │   │   └── HelloWorld.vue
│   │   ├── views/               # 页面视图
│   │   ├── api/                 # API调用模块
│   │   ├── stores/              # Pinia状态管理
│   │   ├── router/              # Vue Router路由
│   │   ├── assets/              # 资源文件
│   │   ├── App.vue              # 根组件
│   │   ├── main.ts              # Vue应用入口
│   │   └── vite-env.d.ts        # TypeScript声明
│   ├── electron/                # Electron相关文件
│   │   ├── main.ts              # Electron主进程
│   │   ├── preload.ts           # 预加载脚本
│   │   └── resources/           # 后端可执行文件
│   │       └── backend.exe      # Go后端程序
│   ├── dist/                    # Vite构建输出
│   ├── dist-electron/           # Electron构建输出
│   ├── index.html               # HTML入口文件
│   ├── package.json             # NPM配置
│   ├── tsconfig.json            # TypeScript配置
│   ├── vite.config.ts           # Vite配置
│   └── electron-builder.json    # Electron Builder配置
├── go-backend/                  # Go后端服务
│   ├── cmd/
│   │   └── server/
│   │       └── main.go          # 服务器入口
│   ├── internal/
│   │   ├── api/                 # API路由处理
│   │   │   ├── handlers/        # 处理器
│   │   │   └── middleware/      # 中间件
│   │   ├── service/             # 业务逻辑层
│   │   ├── model/               # 数据模型
│   │   └── config/              # 配置管理
│   ├── pkg/                     # 公共包
│   ├── go.mod                   # Go模块文件
│   └── go.sum                   # Go依赖锁定
├── docs/                        # 项目文档
│   └── 基础框架设计.md
└── scripts/                     # 构建和部署脚本
    ├── build.sh                 # 构建脚本
    └── dev.sh                   # 开发启动脚本
```

## 核心组件设计

### 1. Electron 主进程 (Main Process)

**职责**:

- 管理应用生命周期
- 创建和管理渲染进程窗口
- 启动 Go 后端服务

**关键功能**:

```javascript
// 启动Go后端服务
function startGoBackend() {
  // 检测端口可用性
  // 启动Go可执行文件
  // 简单健康检查
}

// 窗口管理
function createMainWindow() {
  // 创建主窗口
  // 加载渲染进程
}
```

### 2. Electron 渲染进程 (Renderer Process)

**技术栈**: Vue 3 + Vite + Element Plus

**核心模块**:

- **路由管理**: Vue Router
- **状态管理**: Pinia
- **UI 组件**: Element Plus
- **HTTP 客户端**: Axios

### 3. Go 后端服务

**框架选择**: Gin

**核心模块**:

```go
// 服务器配置
type ServerConfig struct {
    Port string
}

// API路由组
func SetupRoutes(r *gin.Engine) {
    api := r.Group("/api/v1")
    {
        api.GET("/health", healthCheck)
        api.GET("/test", testHandler)
        // 其他业务路由
    }
}
```

## 通信机制

### HTTP API 通信

**请求格式**:

```json
{
  "method": "GET",
  "url": "http://localhost:8080/api/v1/test"
}
```

**响应格式**:

```json
{
  "code": 200,
  "message": "success",
  "data": "Hello from Go backend!"
}
```

## 开发流程

### 1. 项目初始化

#### 前端项目创建

```bash
# 使用 Vite 创建 Vue + TypeScript 项目
npm create vite@latest electron-app -- --template vue-ts

# 进入项目目录
cd electron-app

# 安装依赖 (Node.js 20.16.0 兼容)
npm install

# 安装 Element Plus 和相关依赖
npm install element-plus @element-plus/icons-vue
npm install -D unplugin-vue-components unplugin-auto-import

# 安装 Electron 相关依赖
npm install -D electron electron-builder
npm install -D concurrently wait-on cross-env

# 安装其他前端依赖
npm install axios pinia vue-router@4
```

#### 后端项目创建

```bash
# 创建 Go 项目
mkdir go-backend
cd go-backend

# 初始化 Go 模块
go mod init spreader-tron-backend

# 安装 Gin 框架
go get github.com/gin-gonic/gin
go get github.com/gin-contrib/cors
```

### 2. 开发环境配置

#### Electron 配置文件更新

需要在 `electron-app/package.json` 中添加以下配置：

```json
{
  "main": "dist-electron/main.js",
  "scripts": {
    "dev": "vite",
    "build": "vue-tsc && vite build",
    "preview": "vite preview",
    "electron": "wait-on tcp:5173 && electron .",
    "electron:dev": "concurrently \"npm run dev\" \"npm run electron\"",
    "electron:pack": "npm run build && electron-builder",
    "electron:dist": "npm run build && electron-builder --publish=never"
  },
  "devDependencies": {
    "electron": "^latest",
    "electron-builder": "^latest"
  }
}
```

#### Vite 配置优化

更新 `electron-app/vite.config.ts`：

```typescript
import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";

export default defineConfig({
  plugins: [
    vue(),
    AutoImport({
      resolvers: [ElementPlusResolver()],
    }),
    Components({
      resolvers: [ElementPlusResolver()],
    }),
  ],
  base: "./",
  build: {
    outDir: "dist",
    assetsDir: "assets",
  },
  server: {
    port: 5173,
  },
});
```

### 3. 开发环境启动

```bash
# 启动Go后端 (终端1)
cd go-backend
go run cmd/server/main.go

# 启动Electron开发环境 (终端2)
cd electron-app
npm run electron:dev
```

### 4. 构建流程

```bash
# 构建Go后端
cd go-backend
# Windows
go build -o ../electron-app/resources/backend.exe cmd/server/main.go
# Linux/macOS
go build -o ../electron-app/resources/backend cmd/server/main.go

# 构建Electron应用
cd electron-app
npm run electron:dist
```

## 开发里程碑

### Phase 1: 基础框架搭建 (1-2 周)

#### 1.1 项目初始化 (1-2 天)

- [ ] 使用 `npm create vite@latest` 创建 Vue 3 + TypeScript 项目
- [ ] 安装 Electron 相关依赖 (electron, electron-builder)
- [ ] 安装 Element Plus 和自动导入插件
- [ ] 创建 Go 后端项目结构
- [ ] 配置 Git 仓库和 .gitignore

#### 1.2 Electron 集成 (2-3 天)

- [ ] 创建 Electron 主进程文件 (`electron/main.ts`)
- [ ] 创建预加载脚本 (`electron/preload.ts`)
- [ ] 配置 Vite 支持 Electron 开发
- [ ] 设置开发环境热重载
- [ ] 配置 Electron Builder 打包

#### 1.3 Go 后端服务 (2-3 天)

- [ ] 搭建 Gin 框架基础结构
- [ ] 实现健康检查 API
- [ ] 配置 CORS 中间件
- [ ] 设置日志和错误处理
- [ ] 创建基础 API 路由

#### 1.4 前后端通信 (1-2 天)

- [ ] 配置 Axios HTTP 客户端
- [ ] 实现 API 服务层
- [ ] 测试前后端连通性
- [ ] 添加错误处理和重试机制

### Phase 2: 开发环境优化 (3-5 天)

#### 2.1 开发工具配置

- [ ] 配置 TypeScript 严格模式
- [ ] 设置 ESLint 和 Prettier
- [ ] 配置 Vue DevTools
- [ ] 添加开发脚本和快捷命令

#### 2.2 UI 框架集成

- [ ] 配置 Element Plus 主题
- [ ] 创建基础布局组件
- [ ] 设置路由和状态管理
- [ ] 实现响应式设计

### Phase 3: 核心功能开发 (按业务需求扩展)

#### 3.1 业务功能模块

- [ ] 根据具体需求添加功能模块
- [ ] 实现数据持久化 (SQLite/MySQL)
- [ ] 添加用户认证和权限管理
- [ ] 实现文件上传和处理

#### 3.2 性能和稳定性

- [ ] 前端性能优化 (懒加载、代码分割)
- [ ] 后端性能调优 (缓存、数据库优化)
- [ ] 错误监控和日志系统
- [ ] 单元测试和集成测试

### Phase 4: 部署和发布 (1 周)

#### 4.1 构建优化

- [ ] 优化 Electron 打包体积
- [ ] 配置自动更新机制
- [ ] 设置代码签名 (Windows/macOS)
- [ ] 创建安装程序

#### 4.2 发布准备

- [ ] 编写用户文档
- [ ] 准备发布说明
- [ ] 配置 CI/CD 流水线
- [ ] 测试多平台兼容性

## 技术要求

### 环境要求

- **Node.js**: 20.16.0 (已确认兼容)
- **Go**: 1.24.5 或更高版本
- **操作系统**: Windows 10/11, macOS 10.15+, Ubuntu 18.04+

### 性能目标

- **启动时间**: < 3 秒
- **内存占用**: < 200MB (空闲状态)
- **打包体积**: < 100MB (不含后端数据)
- **API 响应**: < 100ms (本地调用)
